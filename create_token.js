
const CONFIG_KEY = 'ErLk8xmi882CtnfSzF6huFpNkX7z72XViAEHJynCqArm';
const SOL_MINT = 'So11111111111111111111111111111111111111112';

const { DynamicBondingCurveClient } = require('@meteora-ag/dynamic-bonding-curve-sdk');
const { Connection, PublicKey, Keypair, sendAndConfirmTransaction } = require('@solana/web3.js');
const { createConnection } = require('./src/utils/connection');
const { createWallet } = require('./src/utils/wallet');
const config = require('./src/config');
const bs58 = require('bs58');

async function createPool() {
    try {
        // Create Solana connection
        const solConnection = createConnection();

        // Create wallet from private key
        const wallet = createWallet();

        // Initialize the Meteora CP-AMM SDK
        const dbcClient = new DynamicBondingCurveClient(solConnection);

        // Generate a new token mint keypair
        const baseMintKeypair = Keypair.generate();
        console.log(`Generated base mint: ${baseMintKeypair.publicKey.toString()}`);

        // Create the pool transaction
        const transaction = await dbcClient.pool.createPool({
            baseMint: baseMintKeypair.publicKey,
            config: new PublicKey(CONFIG_KEY),
            name: "DO NOT BUY",
            symbol: "DNB",
            uri: "chrome://dino",
            payer: wallet.publicKey,
            poolCreator: wallet.publicKey
        });

        // Get recent blockhash and attach to tx
        const blockhash = await solConnection.getLatestBlockhash();
        transaction.recentBlockhash = blockhash.blockhash;
        transaction.feePayer = wallet.publicKey;

        // Sign the transaction with both the wallet and the base mint keypair
        transaction.sign(wallet, baseMintKeypair);

        // Send and confirm the transaction
        const txHash = await sendAndConfirmTransaction(
            solConnection,
            transaction,
            [wallet, baseMintKeypair]
        );

        console.log(`Pool created successfully!`);
        console.log(`Transaction hash: ${txHash}`);
        console.log(`Base mint address: ${baseMintKeypair.publicKey.toString()}`);
        console.log(`Base mint private key: ${bs58.encode(baseMintKeypair.secretKey)}`);
        console.log(`Save these values for future reference!`);

    } catch (error) {
        console.error('Error creating pool:', error);
        console.error('Error details:', error.stack);
    }
}

// Run the function
createPool().then(() => console.log('Done!'));
