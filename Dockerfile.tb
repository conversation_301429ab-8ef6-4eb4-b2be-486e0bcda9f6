# Use Python 3.12 as base image
FROM python:3.12-slim

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY tweetbot/requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application files
COPY tweetbot/main.py .

# Copy .env file to the working directory
COPY .env .

# Create a volume mount point for persistent data (cookies.json)
# This ensures cookies.json persists bidirectionally in the same directory
VOLUME ["/app"]

# Run the application
CMD ["python", "main.py"]