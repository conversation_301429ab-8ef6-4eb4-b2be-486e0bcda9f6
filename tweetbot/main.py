import asyncio
from dotenv import load_dotenv
from os import environ
from os import listdir, path
from twikit import Client

load_dotenv()

USERNAME = environ['USERNAME']
EMAIL = environ['EMAIL']
PASSWORD = environ['PASSWORD']

# Initialize client
client = Client('en-UK')

def check_cookie_file() -> str:
    if 'cookies.json' in listdir() and path.getsize('cookies.json') > 0:
        return 'cookies.json'
    return None

async def main():
    # Check if cookies are available
    cookie_file = check_cookie_file()
    await client.login(
        auth_info_1=USERNAME,
        auth_info_2=EMAIL,
        password=PASSWORD,
        cookies_file=cookie_file
    )

    await client.create_tweet(
        text='1.61803398875 type shi'
    )

    # Save cookies
    await client.save_cookies('cookies.json')


asyncio.run(main())