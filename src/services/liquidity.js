// Liquidity operations using Meteora CP-AMM SDK
const { Transaction, sendAndConfirmTransaction, Keypair } = require('@solana/web3.js');
const { TOKEN_PROGRAM_ID } = require('@solana/spl-token');
const { BN } = require('@coral-xyz/anchor');
// Removed unused imports - using pool state's sqrtMinPrice and sqrtMaxPrice instead

/**
 * Checks if we need to add to LP based on available balances
 * @param {BN} solBalance - SOL balance
 * @param {BN} tokenBalance - Token balance
 * @returns {boolean} True if we should add to LP
 */
function shouldAddToLP(solBalance, tokenBalance) {
  // Implement your logic here to determine if LP addition is needed
  // For now, we'll assume we should add if both balances are positive
  return !solBalance.isZero() && !tokenBalance.isZero();
}

/**
 * Creates add liquidity instructions using Meteora CP-AMM for existing position
 * Note: This adds liquidity to an existing position instead of creating a new one
 * @param {CpAmm} cpAmmSdk - Meteora CP-AMM SDK instance
 * @param {PublicKey} poolPubkey - Pool public key
 * @param {PublicKey} positionPubkey - Existing position public key
 * @param {PublicKey} positionNftMint - Position NFT mint public key
 * @param {BN} tokenAmount - Token amount to add
 * @param {BN} solAmount - SOL amount to add
 * @param {PublicKey} walletPublicKey - Wallet public key
 * @param {Object} poolState - Pool state from fetchPoolState
 * @returns {Promise<{instructions: Array, liquidityDelta: BN}>} Instructions and liquidity details
 */
async function createAddLiquidityInstructions(cpAmmSdk, poolPubkey, positionPubkey, positionNftTokenAccount, tokenAmount, solAmount, walletPublicKey, poolState) {
  console.log('Creating add liquidity instructions for existing position...');
  // Debug
  console.log(`Pool state sqrt prices - current: ${poolState.sqrtPrice.toString()}, min: ${poolState.sqrtMinPrice.toString()}, max: ${poolState.sqrtMaxPrice.toString()}`);
  console.log(`Token amount: ${tokenAmount.toString()} (${tokenAmount.toNumber() / 1e9} in human units)`);
  console.log(`SOL amount: ${solAmount.toString()} (${solAmount.toNumber() / 1e9} SOL)`);

  // Validate input amounts
  if (tokenAmount.isZero() || solAmount.isZero()) {
    throw new Error('Token amounts cannot be zero');
  }

  const liquidityDelta = await cpAmmSdk.getLiquidityDelta({
    maxAmountTokenA: tokenAmount,
    maxAmountTokenB: solAmount,
    sqrtPrice: poolState.sqrtPrice,
    sqrtMinPrice: poolState.sqrtMinPrice,
    sqrtMaxPrice: poolState.sqrtMaxPrice
  });

  // Calculate minimum acceptable amounts based on slippage
  // const slippageTolerance = 0.05; // 5% slippage tolerance
  // const tokenAAmountThreshold = tokenAmount.mul(new BN(Math.floor((1 - slippageTolerance) * 10000))).div(new BN(10000));
  // const tokenBAmountThreshold = solAmount.mul(new BN(Math.floor((1 - slippageTolerance) * 10000))).div(new BN(10000));

  // Add liquidity to existing position
  const addLiquidityTx = await cpAmmSdk.addLiquidity({
    owner: walletPublicKey,
    pool: poolPubkey,
    position: positionPubkey,
    positionNftAccount: positionNftTokenAccount,
    liquidityDelta: liquidityDelta,
    maxAmountTokenA: tokenAmount,
    maxAmountTokenB: solAmount,
    tokenAAmountThreshold: tokenAmount,
    tokenBAmountThreshold: solAmount,
    tokenAMint: poolState.tokenAMint,
    tokenBMint: poolState.tokenBMint,
    tokenAVault: poolState.tokenAVault,
    tokenBVault: poolState.tokenBVault,
    tokenAProgram: TOKEN_PROGRAM_ID,
    tokenBProgram: TOKEN_PROGRAM_ID
  });

  return {
    instructions: addLiquidityTx.instructions,
    liquidityDelta
  };
}

/**
 * Adds liquidity to an existing position using Meteora CP-AMM
 * @param {CpAmm} cpAmmSdk - Meteora CP-AMM SDK instance
 * @param {Connection} connection - Solana connection
 * @param {PublicKey} poolPubkey - Pool public key
 * @param {PublicKey} positionPubkey - Existing position public key
 * @param {PublicKey} positionNftMint - Position NFT mint public key
 * @param {BN} tokenAmount - Token amount to add
 * @param {BN} solAmount - SOL amount to add
 * @param {Keypair} wallet - Wallet keypair
 * @param {Object} poolState - Pool state from fetchPoolState
 * @returns {Promise<{txHash: string, liquidityDelta: BN}>} Transaction hash and liquidity details
 */
async function addLiquidity(cpAmmSdk, connection, poolPubkey, positionPubkey, positionNftMint, tokenAmount, solAmount, wallet, poolState) {
  console.log('Adding liquidity to existing position...');

  // Create add liquidity instructions
  const { instructions: depositInstructions, liquidityDelta } = await createAddLiquidityInstructions(
    cpAmmSdk,
    poolPubkey,
    positionPubkey,
    positionNftMint,
    tokenAmount,
    solAmount,
    wallet.publicKey,
    poolState
  );

  // Create a new transaction
  const depositTx = new Transaction();

  // Add the instructions
  depositTx.add(...depositInstructions);

  // Get recent blockhash
  const depositBlockhash = await connection.getLatestBlockhash();
  depositTx.recentBlockhash = depositBlockhash.blockhash;
  depositTx.feePayer = wallet.publicKey;

  // Sign the transaction with wallet only (no position NFT keypair needed for existing positions)
  depositTx.sign(wallet);

  // Send and confirm the transaction
  const depositTxHash = await sendAndConfirmTransaction(connection, depositTx, [wallet]);
  console.log(`Liquidity added successfully! Transaction hash: ${depositTxHash}`);
  console.log(`Added to existing position: ${positionPubkey.toString()}`);

  return {
    txHash: depositTxHash,
    liquidityDelta
  };
}
// next the lockLiquidityPermanentInstructions

/**
 * Creates instructions to lock liquidity permanently
 * @param {CpAmm} cpAmmSdk - Meteora CP-AMM SDK instance
 * @param {PublicKey} position - Position public key
 * @param {PublicKey} pool - Pool public key
 * @param {PublicKey} owner - Position owner
 * @param {PublicKey} positionNftMint - Position NFT mint
 * @param {PublicKey} positionNftAccount - Position NFT account
 * @param {BN} unlockedLiquidity - Amount to lock, ex. liquidity delta from addLiquidity
 * @returns {Promise<Array>} Lock liquidity instructions
 */
async function createLockLiquidityPermanentInstructions(cpAmmSdk, position, pool, owner, positionNftMint, positionNftAccount, unlockedLiquidity) {
  const lockLiquidityTx = await cpAmmSdk.permanentLockPosition({
    owner: owner,
    position: position,
    positionNftMint: positionNftMint,
    positionNftAccount: positionNftAccount,
    pool: pool,
    unlockedLiquidity: unlockedLiquidity
  });

  return lockLiquidityTx.instructions;
}


module.exports = {
  shouldAddToLP,
  createAddLiquidityInstructions,
  addLiquidity,
  createLockLiquidityPermanentInstructions
};


