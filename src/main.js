// Main application logic using Meteora CP-AMM SDK
const { CpAmm, getUnClaimReward } = require('@meteora-ag/cp-amm-sdk');
const { BN } = require('@coral-xyz/anchor');
const { Transaction, sendAndConfirmTransaction } = require('@solana/web3.js');

// Import configuration
const config = require('./config');

// Import utilities
const { createConnection } = require('./utils/connection');
const { createWallet, createPublicKey } = require('./utils/wallet');

// Import services
const { getSolBalance } = require('./services/balances');
const { createSwapInstructions } = require('./services/swap');
const { createAddLiquidityInstructions, createLockLiquidityPermanentInstructions } = require('./services/liquidity');
const { createWithdrawVaultInstructions } = require('./services/vault');

/**
 * Main function that orchestrates the entire process using Meteora CP-AMM
 */
async function main() {
  try {
    // Create Solana connection
    const solConnection = createConnection();

    // Create wallet from private key
    const wallet = createWallet();

    // Initialize the Meteora CP-AMM SDK
    const cpAmmSdk = new CpAmm(solConnection);
    const poolAddress = createPublicKey(config.POOL_ADDRESS);
    const positionAddress = createPublicKey(config.POSITION_ADDRESS);
    const positionNftAddress = createPublicKey(config.POSITION_NFT_ADDRESS);
    const positionNftTokenAccount = createPublicKey(config.POSITION_NFT_TOKEN_ACCOUNT);

    // Fetch pool state to get token information
    const poolState = await cpAmmSdk.fetchPoolState(poolAddress);
    console.log(`Token A mint: ${poolState.tokenAMint.toString()}`);
    console.log(`Token B mint: ${poolState.tokenBMint.toString()}`);

    // Fetch position state
    const positionState = await cpAmmSdk.fetchPositionState(positionAddress);

    // Step 1: Keep a list of instructions
    let instructionArray = [];

    // Step 2: Check and store current SOL balance
    let currentSolBalance = await getSolBalance(solConnection, wallet.publicKey);
    console.log(`Initial SOL balance: ${currentSolBalance / 1e9} SOL`);

    // Step 3: Check if there are any fees to claim
    const unclaimedReward = getUnClaimReward(poolState, positionState);
    console.log(`Unclaimed rewards: ${JSON.stringify(unclaimedReward)}`);

    // Convert
    unclaimedTokenA = unclaimedReward.feeTokenA.toString();
    unclaimedTokenB = unclaimedReward.feeTokenB.toString();

    // Check if there are claimable fees (assuming unclaimedReward has tokenA and tokenB amounts)
    const hasClaimableFees = unclaimedReward && (
      (unclaimedReward.feeTokenA && unclaimedReward.feeTokenA.gt(new BN(0))) ||
      (unclaimedReward.feeTokenB && unclaimedReward.feeTokenB.gt(new BN(0)))
    );

    if (hasClaimableFees) {
      console.log('Claimable fees found, adding fee claim instructions...');
      console.log(`Unclaimed Token A: ${unclaimedTokenA / 1e9}`);
      console.log(`Unclaimed Token B: ${unclaimedTokenB / 1e9}`);

      // Add fee claim instructions to the list
      const feeClaimInstructions = await createWithdrawVaultInstructions(
        cpAmmSdk,
        positionAddress,
        poolAddress,
        positionNftAddress,
        positionNftTokenAccount,
        wallet.publicKey,
        poolState
      );
      instructionArray = instructionArray.concat(feeClaimInstructions);

      // Add SOL amount (tokenB) to SOL balance if there are tokenB rewards
      if (unclaimedReward.feeTokenB && unclaimedReward.feeTokenB.gt(new BN(0))) {
        currentSolBalance += unclaimedReward.feeTokenB.toNumber();
        console.log(`Updated SOL balance after claiming fees: ${currentSolBalance / 1e9} SOL`);
      }
    } else {
      console.log('No claimable fees found.');
    }

    // Constants for SOL calculations (in lamports)
    const MIN_BALANCE_LAMPORTS = 0.15 * 1e9; // 0.15 SOL in lamports
    const RESERVE_FOR_FEES_LAMPORTS = 0.05 * 1e9; // 0.05 SOL in lamports

    // Step 4: If the new SOL balance is lower than 0.15, quit
    if (currentSolBalance < MIN_BALANCE_LAMPORTS) {
      console.log(`Not enough balance to continue. Current balance: ${currentSolBalance / 1e9} SOL, minimum required: 0.15 SOL. Exiting...`);
      return;
    }

    // Step 5: Use half of SOL balance minus 0.05 to create swap instructions
    const availableAmount = 0.01 * 1e9;// currentSolBalance - RESERVE_FOR_FEES_LAMPORTS;
    // For testing purposes, set the half available amount to 0.1 SOL

    const halfAvailableAmount = Math.floor(availableAmount / 2);

    if (halfAvailableAmount <= 0) {
      console.log('Not enough balance to swap after reserving for fees. Exiting...');
      return;
    }

    console.log(`Using ${halfAvailableAmount / 1e9} SOL for swap (half of available balance after reserving for fees)...`);

    // Get slot for quote calculation
    const currentSlot = await solConnection.getSlot({ commitment: 'confirmed' });

    // Create swap instructions for the pool - swap token B (SOL) into token A
    const halfAvailableAmountBN = new BN(halfAvailableAmount);
    const { instructions: swapInstructions, expectedOutputAmount, minimumOutputAmount } = await createSwapInstructions(
      cpAmmSdk,
      poolAddress,
      halfAvailableAmountBN,
      config.SLIPPAGE,
      wallet.publicKey,
      poolState,
      currentSlot
    );
    instructionArray = instructionArray.concat(swapInstructions);

    const solAmountForLP = halfAvailableAmountBN;

    // Step 6: Create add liquidity instructions using the new Token A and Token B balances
    // const remainingSolAmount = new BN(halfAvailableAmount); // Use the other half for liquidity
    const { instructions: depositInstructions, liquidityDelta } = await createAddLiquidityInstructions(
      cpAmmSdk,
      poolAddress,
      positionAddress,
      positionNftTokenAccount,
      expectedOutputAmount, // Token A amount from swap
      solAmountForLP,   // Token B (SOL) amount
      wallet.publicKey,
      poolState
    );
    instructionArray = instructionArray.concat(depositInstructions);

    // Step 7: Lock 90% of new liquidity permanently
    const newDelta = liquidityDelta.mul(new BN(90)).div(new BN(100));
    const lockInstructions = await createLockLiquidityPermanentInstructions(
      cpAmmSdk,
      positionAddress,
      poolAddress,
      wallet.publicKey,
      positionNftAddress,
      positionNftTokenAccount,
      newDelta
    );
    instructionArray = instructionArray.concat(lockInstructions);

    // Step 8: Create one large transaction with all these instructions
    const tx = new Transaction();
    tx.add(...instructionArray);

    // Get recent blockhash
    const blockhash = await solConnection.getLatestBlockhash();
    tx.recentBlockhash = blockhash.blockhash;
    tx.feePayer = wallet.publicKey;

    // Sign the transaction
    tx.sign(wallet);

    // Step 8: sendAndConfirmTransaction
    const txHash = await sendAndConfirmTransaction(solConnection, tx, [wallet]);

    // Get final SOL balance
    const finalSolBalance = await getSolBalance(solConnection, wallet.publicKey);
    console.log(`TX hash: ${txHash}`);
    console.log(`Final balance: ${finalSolBalance / 1e9} SOL`);
    console.log(`Added liquidity to existing position: ${positionAddress.toString()}`);

  } catch (error) {
    console.error('Error:', error);
    console.error('Error details:', error.stack);
  }
}

module.exports = main;
