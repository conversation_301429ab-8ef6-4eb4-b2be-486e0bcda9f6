// Configuration for Meteora CP-AMM interaction
require('dotenv').config();

// Environment variables with defaults
const config = {
  PRIVATE_KEY: process.env.PRIVATE_KEY,
  RPC_ENDPOINT: process.env.RPC_ENDPOINT || 'https://api.mainnet-beta.solana.com',
  POOL_ADDRESS: process.env.POOL_ADDRESS,
  POSITION_ADDRESS: process.env.POSITION_ADDRESS,
  POSITION_NFT_ADDRESS: process.env.POSITION_NFT_ADDRESS,
  POSITION_NFT_TOKEN_ACCOUNT: process.env.POSITION_NFT_TOKEN_ACCOUNT,
  SLIPPAGE: process.env.SLIPPAGE ? parseFloat(process.env.SLIPPAGE) : 0.01, // 1% slippage default
};

module.exports = config;
